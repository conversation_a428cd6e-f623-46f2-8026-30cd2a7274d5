#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
根据新的字段结构向飞书多维表格新增3条记录
"""

import subprocess
import json
import time
from datetime import datetime, timedelta

# 配置信息
APP_ID = "cli_a828491ea031d013"
APP_SECRET = "eJsXnjJJVZh9K1XTnR7JbgxBzGbDMlRT"
APP_TOKEN = "TtULb7pBiaGRMgs4dfac4aLAnId"
TABLE_ID = "tblzpzFoRcDR3PC3"  # "数据表"的ID

def execute_curl(method, url, headers=None, data=None):
    """执行curl命令"""
    cmd = ['curl', '-X', method, url, '--insecure', '--silent']

    if headers:
        for k, v in headers.items():
            cmd.extend(['-H', f'{k}: {v}'])
    if data:
        cmd.extend(['-d', data])

    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30,
            encoding='utf-8',
            errors='ignore'
        )

        if result.stdout:
            return json.loads(result.stdout)
        else:
            raise Exception(f"curl返回空响应，返回码: {result.returncode}")

    except json.JSONDecodeError as e:
        raise Exception(f"响应不是有效JSON: {result.stdout}")
    except Exception as e:
        raise Exception(f"curl执行错误: {str(e)}")

def get_access_token():
    """获取访问令牌"""
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    data = json.dumps({
        "app_id": APP_ID,
        "app_secret": APP_SECRET
    })
    headers = {'Content-Type': 'application/json'}
    
    result = execute_curl('POST', url, headers, data)
    return result['tenant_access_token']

def create_sample_records():
    """
    创建3条示例记录数据
    
    Returns:
        List[Dict]: 3条记录的字段数据
    """
    base_time = datetime.now()
    
    records = [
        {
            "发送时间": int(base_time.timestamp() * 1000),
            "发送人": "张三",
            "群聊名称": "技术讨论群",
            "消息内容": "大家好，今天的项目进展如何？有什么需要协助的地方吗？",
            "消息ID": "msg_20250820_001",
            "消息类型": "文本消息",
            "被引用消息ID": ""
        },
        {
            "发送时间": int((base_time + timedelta(minutes=2)).timestamp() * 1000),
            "发送人": "李四",
            "群聊名称": "技术讨论群",
            "消息内容": "项目基本完成了，正在做最后的测试，预计明天可以上线",
            "消息ID": "msg_20250820_002",
            "消息类型": "回复消息",
            "被引用消息ID": "msg_20250820_001"
        },
        {
            "发送时间": int((base_time + timedelta(minutes=5)).timestamp() * 1000),
            "发送人": "王五",
            "群聊名称": "技术讨论群",
            "消息内容": "太好了！我这边也准备好了相关文档和部署脚本，随时可以配合上线",
            "消息ID": "msg_20250820_003",
            "消息类型": "回复消息",
            "被引用消息ID": "msg_20250820_002"
        }
    ]
    
    return records

def add_records_batch(records_data):
    """
    批量新增记录
    
    Args:
        records_data: 记录数据列表
        
    Returns:
        List[Dict]: 新增记录的结果列表
    """
    # 获取访问令牌
    token = get_access_token()
    print(f"✅ 成功获取访问令牌: {token[:20]}...")
    
    # 准备批量记录数据
    records = []
    for record_data in records_data:
        records.append({"fields": record_data})
    
    # API调用
    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{APP_TOKEN}/tables/{TABLE_ID}/records/batch_create"
    data = json.dumps({"records": records})
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    result = execute_curl('POST', url, headers, data)
    
    if result.get('code') == 0:
        records_result = result.get('data', {}).get('records', [])
        print(f"✅ 成功批量新增 {len(records_result)} 条记录")
        return records_result
    else:
        raise Exception(f"批量新增记录失败: {result.get('msg', '未知错误')}")

def main():
    """主函数"""
    print("=== 向飞书多维表格新增3条记录 ===")
    print(f"表格ID: {APP_TOKEN}")
    print(f"数据表ID: {TABLE_ID}")
    print()
    
    try:
        # 创建3条示例记录
        print("1. 准备记录数据...")
        records_data = create_sample_records()
        
        for i, record in enumerate(records_data, 1):
            print(f"\n记录{i}:")
            print(f"  发送时间: {datetime.fromtimestamp(record['发送时间']/1000).strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"  发送人: {record['发送人']}")
            print(f"  群聊名称: {record['群聊名称']}")
            print(f"  消息内容: {record['消息内容']}")
            print(f"  消息类型: {record['消息类型']}")
            print(f"  消息ID: {record['消息ID']}")
            if record['被引用消息ID']:
                print(f"  被引用消息ID: {record['被引用消息ID']}")
        
        # 批量新增记录
        print("\n2. 开始新增记录...")
        results = add_records_batch(records_data)
        
        # 显示结果
        print("\n3. 新增结果:")
        for i, result in enumerate(results, 1):
            record_id = result.get('record_id')
            print(f"  记录{i} - ID: {record_id}")
        
        print(f"\n🎉 成功完成！共新增了 {len(results)} 条记录")
        print("请检查您的飞书多维表格，应该可以看到新增的记录。")
        
    except Exception as e:
        print(f"❌ 操作失败: {str(e)}")
        print("\n可能的原因:")
        print("1. 网络连接问题")
        print("2. 字段名称不匹配")
        print("3. 应用权限不足")
        print("4. 表格ID或数据表ID错误")

if __name__ == "__main__":
    main()
