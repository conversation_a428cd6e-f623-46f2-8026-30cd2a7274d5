# 飞书多维表格API操作指南

## 概述

本文档提供了通过飞书开放平台API向多维表格新增记录的完整操作方法，包括API调用流程、代码实现和常见问题解决方案。

## 前置条件

### 1. 飞书应用信息
- **应用类型**: 企业自建应用
- **必需权限**: 
  - `bitable:app` (查看、评论、编辑和管理多维表格)
  - `bitable:app:readonly` (查看多维表格)

### 2. 开发环境
- Python 3.6+
- curl命令（推荐方案）
- 网络连接

## API调用流程

### 步骤1: 获取访问令牌

**接口**: `POST https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal`

**请求体**:
```json
{
    "app_id": "your_app_id",
    "app_secret": "your_app_secret"
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "ok",
    "tenant_access_token": "t-g1048ihGCLFGGVHL5FPR4NVZEV53VWOWK5TD4ONG",
    "expire": 5878
}
```

### 步骤2: 解析多维表格URL

从飞书多维表格URL中提取`app_token`:
- **URL格式**: `https://xxx.feishu.cn/base/{app_token}`
- **示例**: `https://gwvwal7cgy.feishu.cn/base/TtULb7pBiaGRMgs4dfac4aLAnId`
- **app_token**: `TtULb7pBiaGRMgs4dfac4aLAnId`

### 步骤3: 获取数据表列表

**接口**: `GET https://open.feishu.cn/open-apis/bitable/v1/apps/{app_token}/tables`

**请求头**:
```
Authorization: Bearer {access_token}
Content-Type: application/json
```

**响应示例**:
```json
{
    "code": 0,
    "data": {
        "items": [
            {
                "table_id": "tblzpzFoRcDR3PC3",
                "name": "数据表",
                "revision": 1
            }
        ]
    }
}
```

### 步骤4: 新增记录

#### 新增单条记录

**接口**: `POST https://open.feishu.cn/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records`

**请求体**:
```json
{
    "fields": {
        "消息时间": 1703123456789,
        "发送人": "张三",
        "消息内容": "这是一条测试消息"
    }
}
```

#### 批量新增记录

**接口**: `POST https://open.feishu.cn/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records/batch_create`

**请求体**:
```json
{
    "records": [
        {
            "fields": {
                "消息时间": 1703123456789,
                "发送人": "用户1",
                "消息内容": "消息1"
            }
        },
        {
            "fields": {
                "消息时间": 1703123456790,
                "发送人": "用户2", 
                "消息内容": "消息2"
            }
        }
    ]
}
```

## 字段数据格式

### 常用字段类型及格式

| 字段类型 | API格式 | 示例 | 说明 |
|---------|---------|------|------|
| 文本 | 字符串 | `"张三"` | 直接使用字符串 |
| 数字 | 数字 | `100` | 整数或小数 |
| 日期时间 | 毫秒时间戳 | `1703123456789` | 13位毫秒时间戳 |
| 单选 | 字符串 | `"选项1"` | 选项名称 |
| 多选 | 字符串数组 | `["选项1", "选项2"]` | 选项名称数组 |
| 复选框 | 布尔值 | `true` | true/false |
| 附件 | 对象数组 | `[{"file_token": "xxx"}]` | 需先上传文件 |
| 人员 | 对象数组 | `[{"id": "ou_xxx"}]` | 用户ID |
| 超链接 | 对象 | `{"text": "链接", "link": "https://xxx"}` | 文本和链接 |

### 时间戳转换

```python
from datetime import datetime

# 当前时间转毫秒时间戳
timestamp = int(datetime.now().timestamp() * 1000)

# 指定时间转时间戳
dt = datetime(2024, 1, 1, 12, 0, 0)
timestamp = int(dt.timestamp() * 1000)
```

## 代码实现

### 方案1: 使用curl（推荐）

```python
import subprocess
import json
from datetime import datetime

def execute_curl(method, url, headers=None, data=None):
    """执行curl命令"""
    curl_command = ['curl', '-X', method, url, '--insecure', '--silent']
    
    if headers:
        for key, value in headers.items():
            curl_command.extend(['-H', f'{key}: {value}'])
    
    if data:
        curl_command.extend(['-d', data])
    
    result = subprocess.run(curl_command, capture_output=True, text=True, timeout=30)
    return json.loads(result.stdout)

def get_access_token(app_id, app_secret):
    """获取访问令牌"""
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    payload = json.dumps({"app_id": app_id, "app_secret": app_secret})
    headers = {'Content-Type': 'application/json'}
    
    result = execute_curl('POST', url, headers, payload)
    return result['tenant_access_token']

def add_record(app_token, table_id, fields, access_token):
    """新增记录"""
    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records"
    payload = json.dumps({"fields": fields})
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    result = execute_curl('POST', url, headers, payload)
    return result['data']['record']

# 使用示例
app_id = "your_app_id"
app_secret = "your_app_secret"
app_token = "your_app_token"
table_id = "your_table_id"

# 获取令牌
token = get_access_token(app_id, app_secret)

# 新增记录
fields = {
    "消息时间": int(datetime.now().timestamp() * 1000),
    "发送人": "测试用户",
    "消息内容": "这是一条测试消息"
}

record = add_record(app_token, table_id, fields, token)
print(f"记录ID: {record['record_id']}")
```

### 方案2: 使用requests

```python
import requests
import json
from datetime import datetime

def get_access_token(app_id, app_secret):
    """获取访问令牌"""
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    payload = {"app_id": app_id, "app_secret": app_secret}
    headers = {'Content-Type': 'application/json'}
    
    response = requests.post(url, headers=headers, json=payload, verify=False)
    return response.json()['tenant_access_token']

def add_record(app_token, table_id, fields, access_token):
    """新增记录"""
    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records"
    payload = {"fields": fields}
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    response = requests.post(url, headers=headers, json=payload, verify=False)
    return response.json()['data']['record']
```

## 常见问题及解决方案

### 1. 网络连接问题

**问题**: SSL错误、代理错误
**解决方案**: 
- 使用curl方案（推荐）
- 在requests中添加`verify=False`
- 检查网络代理设置

### 2. 权限问题

**问题**: 返回权限不足错误
**解决方案**:
- 确保应用已添加到多维表格
- 检查应用权限配置
- 验证app_id和app_secret

### 3. 字段名称不匹配

**问题**: 字段不存在错误
**解决方案**:
- 确保字段名完全匹配（包括大小写、空格）
- 先调用获取表格字段接口确认字段名
- 检查字段类型是否支持API写入

### 4. 数据格式错误

**问题**: 数据类型不匹配
**解决方案**:
- 日期字段使用毫秒时间戳
- 文本字段使用字符串
- 数字字段使用数值类型
- 参考字段格式对照表

## 测试验证

### 快速测试脚本

```python
def test_api():
    """API连接测试"""
    app_id = "your_app_id"
    app_secret = "your_app_secret"
    
    try:
        # 测试获取令牌
        token = get_access_token(app_id, app_secret)
        print(f"✅ 令牌获取成功: {token[:20]}...")
        
        # 测试新增记录
        fields = {
            "消息时间": int(datetime.now().timestamp() * 1000),
            "发送人": "测试用户",
            "消息内容": "API测试消息"
        }
        
        record = add_record("app_token", "table_id", fields, token)
        print(f"✅ 记录新增成功: {record['record_id']}")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

test_api()
```

## 最佳实践

1. **令牌管理**: 缓存访问令牌，避免频繁获取
2. **错误处理**: 添加完整的异常处理机制
3. **批量操作**: 优先使用批量接口提高效率
4. **频率控制**: 遵守API调用频率限制
5. **数据验证**: 在发送前验证数据格式

## 扩展功能

基于新增记录的基础，可以扩展实现：
- 查询记录: `GET /records/search`
- 更新记录: `PUT /records/{record_id}`
- 删除记录: `DELETE /records/{record_id}`
- 文件上传: `POST /drive/v1/medias/upload_all`

---

**注意**: 本文档基于飞书开放平台API v1版本，使用前请确认API版本和权限配置。
