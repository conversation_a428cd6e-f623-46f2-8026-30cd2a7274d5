# 飞书多维表格API操作指南

本项目提供了通过飞书开放平台API向多维表格新增记录的完整解决方案。

## ✅ 测试结果

**API连接测试已成功！** 您的配置信息正确，已成功：
- 获取访问令牌
- 连接到您的多维表格
- 发现2个数据表："数据表" 和 "API Test Table"
- 成功新增单条和批量记录

## 项目文件说明

- `feishu_bitable_api.py` - 核心API操作类（使用requests）
- `feishu_api_curl.py` - 使用curl的API操作类（推荐使用，已测试成功）
- `example_usage.py` - 使用示例
- `test_api_connection.py` - API连接测试工具
- `requirements.txt` - Python依赖包
- `README.md` - 使用说明文档

## 环境要求

- Python 3.6+
- requests库

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置信息

根据您提供的信息，已配置：

- **APPID**: `cli_a828491ea031d013`
- **App Secret**: `eJsXnjJJVZh9K1XTnR7JbgxBzGbDMlRT`
- **多维表格链接**: `https://gwvwal7cgy.feishu.cn/base/TtULb7pBiaGRMgs4dfac4aLAnId`

## 表格字段说明

您的多维表格包含以下字段：

1. **消息时间** - 日期格式（API中使用毫秒时间戳）
2. **发送人** - 文本格式
3. **消息内容** - 文本格式  
4. **消息附件** - 附件格式（需要先上传文件获取file_token）

## 快速开始

### 1. 运行测试示例（推荐使用curl版本）

```bash
# 使用curl版本（已测试成功）
python feishu_api_curl.py

# 或者使用requests版本（可能有网络问题）
python example_usage.py
```

### 2. 自定义使用

```python
from feishu_api_curl import FeishuBitableAPICurl
from datetime import datetime

# 初始化API客户端
api = FeishuBitableAPICurl("cli_a828491ea031d013", "eJsXnjJJVZh9K1XTnR7JbgxBzGbDMlRT")

# 准备记录数据
fields = {
    "消息时间": int(datetime.now().timestamp() * 1000),  # 毫秒时间戳
    "发送人": "张三",
    "消息内容": "这是一条测试消息"
}

# 新增记录
app_token = "TtULb7pBiaGRMgs4dfac4aLAnId"  # 从URL中提取
table_id = "tblzpzFoRcDR3PC3"  # 您的"数据表"的ID

result = api.add_record(app_token, table_id, fields)
print(f"记录ID: {result['record_id']}")
```

### 3. 您的表格信息

根据测试结果，您的多维表格包含：
- **表格1**: "数据表" (ID: tblzpzFoRcDR3PC3)
- **表格2**: "API Test Table" (ID: tblqxzIitUCd0Llq)

## API功能说明

### 1. 获取访问令牌

```python
token = api.get_tenant_access_token()
```

### 2. 获取数据表列表

```python
tables = api.get_tables(app_token)
for table in tables:
    print(f"表名: {table['name']}, ID: {table['table_id']}")
```

### 3. 新增单条记录

```python
fields = {
    "消息时间": 1703123456789,  # 毫秒时间戳
    "发送人": "用户名",
    "消息内容": "消息内容"
}
result = api.add_record(app_token, table_id, fields)
```

### 4. 批量新增记录

```python
records = [
    {"fields": {"发送人": "用户1", "消息内容": "消息1"}},
    {"fields": {"发送人": "用户2", "消息内容": "消息2"}}
]
results = api.add_records_batch(app_token, table_id, records)
```

## 字段数据格式说明

### 日期时间字段
- 使用毫秒时间戳格式
- 示例：`int(datetime.now().timestamp() * 1000)`

### 文本字段
- 直接使用字符串
- 示例：`"这是文本内容"`

### 附件字段
- 需要先上传文件到飞书获取file_token
- 格式：`[{"file_token": "文件token"}]`

## 常见问题

### 1. 权限问题
确保您的飞书应用已经：
- 开通了多维表格相关权限
- 被添加到目标多维表格中
- 具有写入权限

### 2. 字段名称不匹配
请确保代码中的字段名与多维表格中的实际字段名完全一致，包括：
- 字段名称的大小写
- 特殊字符和空格

### 3. 数据格式错误
不同字段类型需要特定的数据格式：
- 日期：毫秒时间戳（数字）
- 文本：字符串
- 附件：包含file_token的字典列表

### 4. API调用频率限制
飞书API有调用频率限制，如果遇到限制，请：
- 减少调用频率
- 使用批量接口减少调用次数
- 添加适当的延迟

## 错误处理

代码中包含了完整的错误处理机制：

```python
try:
    result = api.add_record(app_token, table_id, fields)
    print("成功新增记录")
except Exception as e:
    print(f"操作失败: {str(e)}")
```

## 注意事项

1. **访问令牌有效期**: 约2小时，代码会自动刷新
2. **字段名称**: 必须与表格中的实际字段名完全匹配
3. **数据类型**: 确保传入的数据类型符合字段要求
4. **权限设置**: 确保应用有足够的权限访问和修改表格

## 扩展功能

如需实现更多功能，可以参考飞书开放平台文档：
- 查询记录
- 更新记录  
- 删除记录
- 上传附件

## 技术支持

如遇到问题，请检查：
1. 网络连接是否正常
2. 应用配置是否正确
3. 权限设置是否完整
4. 字段名称和数据格式是否正确
