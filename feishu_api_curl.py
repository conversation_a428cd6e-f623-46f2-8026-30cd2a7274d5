#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用curl命令的飞书多维表格API操作类
解决Python requests的网络连接问题
"""

import subprocess
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional

class FeishuBitableAPICurl:
    """使用curl命令的飞书多维表格API操作类"""
    
    def __init__(self, app_id: str, app_secret: str):
        """
        初始化飞书API客户端
        
        Args:
            app_id: 飞书应用ID
            app_secret: 飞书应用密钥
        """
        self.app_id = app_id
        self.app_secret = app_secret
        self.base_url = "https://open.feishu.cn/open-apis"
        self.access_token = None
        self.token_expires_at = 0
        
    def _execute_curl(self, method: str, url: str, headers: Dict[str, str] = None, data: str = None) -> Dict:
        """
        执行curl命令
        
        Args:
            method: HTTP方法 (GET, POST, PUT, DELETE)
            url: 请求URL
            headers: 请求头
            data: 请求数据
            
        Returns:
            Dict: 响应结果
        """
        curl_command = ['curl', '-X', method, url, '--insecure', '--connect-timeout', '30', '--silent']
        
        # 添加请求头
        if headers:
            for key, value in headers.items():
                curl_command.extend(['-H', f'{key}: {value}'])
        
        # 添加请求数据
        if data:
            curl_command.extend(['-d', data])
        
        try:
            result = subprocess.run(
                curl_command,
                capture_output=True,
                text=True,
                timeout=30,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0 and result.stdout:
                try:
                    return json.loads(result.stdout)
                except json.JSONDecodeError as e:
                    raise Exception(f"响应不是有效JSON: {result.stdout}")
            else:
                # 忽略stderr中的进度信息，只关注实际错误
                if result.stderr and "%" not in result.stderr:
                    raise Exception(f"curl命令失败: 返回码={result.returncode}, 错误={result.stderr}")
                elif result.returncode != 0:
                    raise Exception(f"curl命令失败: 返回码={result.returncode}")
                else:
                    raise Exception("curl命令返回空响应")

        except subprocess.TimeoutExpired:
            raise Exception("curl命令超时")
        except FileNotFoundError:
            raise Exception("系统中未找到curl命令，请安装curl")
        except Exception as e:
            raise Exception(f"curl执行错误: {str(e)}")
    
    def get_tenant_access_token(self) -> str:
        """
        获取tenant_access_token
        
        Returns:
            str: access token
        """
        # 检查token是否过期
        if self.access_token and time.time() < self.token_expires_at:
            return self.access_token
            
        url = f"{self.base_url}/auth/v3/tenant_access_token/internal"
        
        payload = {
            "app_id": self.app_id,
            "app_secret": self.app_secret
        }
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        try:
            result = self._execute_curl('POST', url, headers, json.dumps(payload))
            
            if result.get('code') == 0:
                self.access_token = result['tenant_access_token']
                # token有效期通常是2小时，这里设置为1.5小时后过期
                self.token_expires_at = time.time() + 5400
                print(f"成功获取access_token: {self.access_token[:20]}...")
                return self.access_token
            else:
                raise Exception(f"获取access_token失败: {result.get('msg', '未知错误')}")
                
        except Exception as e:
            raise Exception(f"请求失败: {str(e)}")
    
    def extract_app_token_and_table_id(self, bitable_url: str) -> tuple:
        """
        从飞书多维表格URL中提取app_token和table_id
        
        Args:
            bitable_url: 飞书多维表格链接
            
        Returns:
            tuple: (app_token, table_id)
        """
        try:
            # URL格式: https://gwvwal7cgy.feishu.cn/base/TtULb7pBiaGRMgs4dfac4aLAnId
            # app_token就是base/后面的部分
            if '/base/' in bitable_url:
                app_token = bitable_url.split('/base/')[-1].split('?')[0]
                print(f"提取到app_token: {app_token}")
                
                # 对于table_id，我们需要获取第一个表格的ID
                # 这里先返回app_token，table_id需要通过API获取
                return app_token, None
            else:
                raise ValueError("无效的飞书多维表格URL格式")
                
        except Exception as e:
            raise Exception(f"解析URL失败: {str(e)}")
    
    def get_tables(self, app_token: str) -> List[Dict]:
        """
        获取多维表格中的所有数据表
        
        Args:
            app_token: 多维表格的app_token
            
        Returns:
            List[Dict]: 数据表列表
        """
        access_token = self.get_tenant_access_token()
        
        url = f"{self.base_url}/bitable/v1/apps/{app_token}/tables"
        
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        try:
            result = self._execute_curl('GET', url, headers)
            
            if result.get('code') == 0:
                tables = result.get('data', {}).get('items', [])
                print(f"获取到 {len(tables)} 个数据表")
                for table in tables:
                    print(f"  - 表名: {table.get('name')}, table_id: {table.get('table_id')}")
                return tables
            else:
                raise Exception(f"获取数据表失败: {result.get('msg', '未知错误')}")
                
        except Exception as e:
            raise Exception(f"请求失败: {str(e)}")
    
    def add_record(self, app_token: str, table_id: str, fields: Dict[str, Any]) -> Dict:
        """
        新增单条记录
        
        Args:
            app_token: 多维表格的app_token
            table_id: 数据表的table_id
            fields: 字段数据，格式为 {"字段名": "字段值"}
            
        Returns:
            Dict: 新增记录的结果
        """
        access_token = self.get_tenant_access_token()
        
        url = f"{self.base_url}/bitable/v1/apps/{app_token}/tables/{table_id}/records"
        
        payload = {
            "fields": fields
        }
        
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        try:
            result = self._execute_curl('POST', url, headers, json.dumps(payload))
            
            if result.get('code') == 0:
                record_data = result.get('data', {}).get('record', {})
                print(f"成功新增记录，record_id: {record_data.get('record_id')}")
                return record_data
            else:
                raise Exception(f"新增记录失败: {result.get('msg', '未知错误')}")
                
        except Exception as e:
            raise Exception(f"请求失败: {str(e)}")
    
    def add_records_batch(self, app_token: str, table_id: str, records: List[Dict[str, Any]]) -> List[Dict]:
        """
        批量新增记录
        
        Args:
            app_token: 多维表格的app_token
            table_id: 数据表的table_id
            records: 记录列表，每个记录格式为 {"fields": {"字段名": "字段值"}}
            
        Returns:
            List[Dict]: 新增记录的结果列表
        """
        access_token = self.get_tenant_access_token()
        
        url = f"{self.base_url}/bitable/v1/apps/{app_token}/tables/{table_id}/records/batch_create"
        
        payload = {
            "records": records
        }
        
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        try:
            result = self._execute_curl('POST', url, headers, json.dumps(payload))
            
            if result.get('code') == 0:
                records_data = result.get('data', {}).get('records', [])
                print(f"成功批量新增 {len(records_data)} 条记录")
                return records_data
            else:
                raise Exception(f"批量新增记录失败: {result.get('msg', '未知错误')}")
                
        except Exception as e:
            raise Exception(f"请求失败: {str(e)}")


def create_sample_record(record_num: int = 1) -> Dict[str, Any]:
    """
    创建示例记录数据

    Args:
        record_num: 记录编号，用于区分不同记录

    Returns:
        Dict: 示例记录的字段数据
    """
    # 当前时间戳（毫秒）
    current_timestamp = int(datetime.now().timestamp() * 1000)

    # 根据记录编号创建不同的示例数据
    sample_data = [
        {
            "发送时间": current_timestamp,
            "发送人": "张三",
            "群聊名称": "技术讨论群",
            "消息内容": "大家好，今天的项目进展如何？",
            "引用消息ID": "",
            "消息类型": "文本消息",
            "被引用消息ID": ""
        },
        {
            "发送时间": current_timestamp + 60000,  # 1分钟后
            "发送人": "李四",
            "群聊名称": "技术讨论群",
            "消息内容": "项目基本完成了，正在做最后的测试",
            "引用消息ID": "msg_001",
            "消息类型": "回复消息",
            "被引用消息ID": "msg_001"
        },
        {
            "发送时间": current_timestamp + 120000,  # 2分钟后
            "发送人": "王五",
            "群聊名称": "技术讨论群",
            "消息内容": "太好了！我这边也准备好了相关文档",
            "引用消息ID": "",
            "消息类型": "文本消息",
            "被引用消息ID": ""
        }
    ]

    # 返回指定编号的记录，如果超出范围则返回最后一个
    index = min(record_num - 1, len(sample_data) - 1)
    return sample_data[index]


def main():
    """主函数"""
    # 配置信息
    APP_ID = "cli_a828491ea031d013"
    APP_SECRET = "eJsXnjJJVZh9K1XTnR7JbgxBzGbDMlRT"
    BITABLE_URL = "https://gwvwal7cgy.feishu.cn/base/TtULb7pBiaGRMgs4dfac4aLAnId"
    
    print("=== 飞书多维表格API测试 (使用curl) ===")
    print(f"应用ID: {APP_ID}")
    print(f"表格链接: {BITABLE_URL}")
    print()
    
    try:
        # 初始化API客户端
        print("1. 初始化API客户端...")
        api = FeishuBitableAPICurl(APP_ID, APP_SECRET)
        
        # 获取访问令牌
        print("2. 获取访问令牌...")
        token = api.get_tenant_access_token()
        
        # 提取app_token
        print("3. 解析表格URL...")
        app_token, _ = api.extract_app_token_and_table_id(BITABLE_URL)
        
        # 获取数据表列表
        print("4. 获取数据表列表...")
        tables = api.get_tables(app_token)
        
        if not tables:
            print("未找到任何数据表")
            return
        
        # 使用第一个表格
        table_id = tables[0]['table_id']
        table_name = tables[0]['name']
        print(f"\n使用数据表: {table_name} (ID: {table_id})")
        
        # 新增3条记录
        print("\n5. 新增3条测试记录...")

        # 准备3条记录的数据
        batch_records = []
        for i in range(3):
            fields = create_sample_record(i + 1)
            batch_records.append({"fields": fields})
            print(f"记录{i+1}: {fields}")

        # 批量新增记录
        batch_result = api.add_records_batch(app_token, table_id, batch_records)
        print(f"\n✅ 批量新增成功，共 {len(batch_result)} 条记录")

        # 显示新增记录的详细信息
        for i, record in enumerate(batch_result):
            print(f"  记录{i+1} ID: {record.get('record_id')}")
        
        print("\n=== 测试完成 ===")
        print("🎉 所有操作成功完成！")
        print("请检查您的飞书多维表格，应该可以看到新增的记录。")
        
    except Exception as e:
        print(f"❌ 操作失败: {str(e)}")


if __name__ == "__main__":
    main()
