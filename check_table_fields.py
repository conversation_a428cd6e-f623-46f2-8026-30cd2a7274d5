#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查飞书多维表格的字段结构
"""

import subprocess
import json

# 配置信息
APP_ID = "cli_a828491ea031d013"
APP_SECRET = "eJsXnjJJVZh9K1XTnR7JbgxBzGbDMlRT"
APP_TOKEN = "TtULb7pBiaGRMgs4dfac4aLAnId"
TABLE_ID = "tblzpzFoRcDR3PC3"

def execute_curl(method, url, headers=None, data=None):
    """执行curl命令"""
    cmd = ['curl', '-X', method, url, '--insecure', '--silent']
    
    if headers:
        for k, v in headers.items():
            cmd.extend(['-H', f'{k}: {v}'])
    if data:
        cmd.extend(['-d', data])
    
    try:
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=30,
            encoding='utf-8',
            errors='ignore'
        )
        
        if result.stdout:
            return json.loads(result.stdout)
        else:
            raise Exception(f"curl返回空响应，返回码: {result.returncode}")
            
    except json.JSONDecodeError as e:
        raise Exception(f"响应不是有效JSON: {result.stdout}")
    except Exception as e:
        raise Exception(f"curl执行错误: {str(e)}")

def get_access_token():
    """获取访问令牌"""
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    data = json.dumps({
        "app_id": APP_ID,
        "app_secret": APP_SECRET
    })
    headers = {'Content-Type': 'application/json'}
    
    result = execute_curl('POST', url, headers, data)
    return result['tenant_access_token']

def get_table_fields():
    """获取数据表字段信息"""
    token = get_access_token()
    print(f"✅ 成功获取访问令牌: {token[:20]}...")
    
    # 获取字段信息
    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{APP_TOKEN}/tables/{TABLE_ID}/fields"
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    result = execute_curl('GET', url, headers)
    
    if result.get('code') == 0:
        fields = result.get('data', {}).get('items', [])
        print(f"\n📋 数据表字段信息 (共{len(fields)}个字段):")
        print("-" * 60)
        
        for i, field in enumerate(fields, 1):
            field_name = field.get('field_name', '未知')
            field_type = field.get('type', 0)
            ui_type = field.get('ui_type', '未知')
            field_id = field.get('field_id', '未知')
            
            print(f"{i:2d}. 字段名: {field_name}")
            print(f"    字段ID: {field_id}")
            print(f"    类型: {field_type} ({ui_type})")
            print()
        
        return fields
    else:
        raise Exception(f"获取字段信息失败: {result.get('msg', '未知错误')}")

def test_simple_record():
    """测试添加一条简单记录"""
    token = get_access_token()
    
    # 使用最基本的字段进行测试
    fields = {
        "发送人": "测试用户"
    }
    
    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{APP_TOKEN}/tables/{TABLE_ID}/records"
    data = json.dumps({"fields": fields})
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    print("🧪 测试添加简单记录...")
    print(f"测试数据: {fields}")
    
    try:
        result = execute_curl('POST', url, headers, data)
        
        if result.get('code') == 0:
            record_id = result.get('data', {}).get('record', {}).get('record_id')
            print(f"✅ 测试成功！记录ID: {record_id}")
            return True
        else:
            print(f"❌ 测试失败: {result.get('msg', '未知错误')}")
            print(f"完整响应: {result}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False

def main():
    """主函数"""
    print("=== 检查飞书多维表格字段结构 ===")
    print(f"表格ID: {APP_TOKEN}")
    print(f"数据表ID: {TABLE_ID}")
    
    try:
        # 获取字段信息
        fields = get_table_fields()
        
        # 测试简单记录添加
        print("\n" + "="*60)
        test_success = test_simple_record()
        
        if test_success:
            print("\n✅ 基础连接测试成功，可以继续添加完整记录")
        else:
            print("\n❌ 基础连接测试失败，请检查配置")
        
    except Exception as e:
        print(f"❌ 操作失败: {str(e)}")

if __name__ == "__main__":
    main()
