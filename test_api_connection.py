#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试飞书API连接
"""

import requests
import json
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_feishu_api():
    """测试飞书API连接"""
    
    # 配置信息
    APP_ID = "cli_a828491ea031d013"
    APP_SECRET = "eJsXnjJJVZh9K1XTnR7JbgxBzGbDMlRT"
    
    print("=== 测试飞书API连接 ===")
    print(f"APP_ID: {APP_ID}")
    print(f"APP_SECRET: {APP_SECRET[:10]}...")
    
    # 获取访问令牌的URL
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    
    # 请求数据
    payload = {
        "app_id": APP_ID,
        "app_secret": APP_SECRET
    }
    
    # 请求头
    headers = {
        'Content-Type': 'application/json'
    }
    
    print(f"\n请求URL: {url}")
    print(f"请求数据: {payload}")
    
    try:
        # 尝试不同的请求配置
        print("\n=== 尝试1: 标准请求 ===")
        response = requests.post(
            url, 
            headers=headers, 
            data=json.dumps(payload),
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 0:
                token = result.get('tenant_access_token')
                print(f"✅ 成功获取访问令牌: {token[:20]}...")
                return token
            else:
                print(f"❌ API返回错误: {result}")
        
    except requests.exceptions.SSLError as e:
        print(f"SSL错误: {e}")
        print("\n=== 尝试2: 禁用SSL验证 ===")
        try:
            response = requests.post(
                url, 
                headers=headers, 
                data=json.dumps(payload),
                timeout=30,
                verify=False
            )
            
            print(f"状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    token = result.get('tenant_access_token')
                    print(f"✅ 成功获取访问令牌: {token[:20]}...")
                    return token
                else:
                    print(f"❌ API返回错误: {result}")
                    
        except Exception as e2:
            print(f"第二次尝试也失败: {e2}")
    
    except requests.exceptions.ProxyError as e:
        print(f"代理错误: {e}")
        print("请检查网络代理设置")
        
    except requests.exceptions.ConnectionError as e:
        print(f"连接错误: {e}")
        print("请检查网络连接")
        
    except requests.exceptions.Timeout as e:
        print(f"超时错误: {e}")
        print("请检查网络速度")
        
    except Exception as e:
        print(f"其他错误: {e}")
    
    return None


def test_with_curl():
    """使用curl命令测试"""
    import subprocess
    
    print("\n=== 使用curl测试 ===")
    
    curl_command = [
        'curl',
        '-X', 'POST',
        'https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal',
        '-H', 'Content-Type: application/json',
        '-d', '{"app_id":"cli_a828491ea031d013","app_secret":"eJsXnjJJVZh9K1XTnR7JbgxBzGbDMlRT"}',
        '--insecure',  # 忽略SSL证书验证
        '--connect-timeout', '30'
    ]
    
    try:
        result = subprocess.run(curl_command, capture_output=True, text=True, timeout=30)
        print(f"curl返回码: {result.returncode}")
        print(f"curl输出: {result.stdout}")
        if result.stderr:
            print(f"curl错误: {result.stderr}")
            
        if result.returncode == 0 and result.stdout:
            try:
                response_data = json.loads(result.stdout)
                if response_data.get('code') == 0:
                    token = response_data.get('tenant_access_token')
                    print(f"✅ curl成功获取令牌: {token[:20]}...")
                    return token
                else:
                    print(f"❌ curl API返回错误: {response_data}")
            except json.JSONDecodeError:
                print("❌ curl返回的不是有效JSON")
                
    except subprocess.TimeoutExpired:
        print("❌ curl命令超时")
    except FileNotFoundError:
        print("❌ 系统中未找到curl命令")
    except Exception as e:
        print(f"❌ curl执行错误: {e}")
    
    return None


if __name__ == "__main__":
    # 测试Python requests
    token = test_feishu_api()
    
    # 如果Python requests失败，尝试curl
    if not token:
        token = test_with_curl()
    
    if token:
        print(f"\n🎉 API连接测试成功！")
        print(f"访问令牌: {token}")
        print("\n接下来可以使用这个令牌进行其他API操作。")
    else:
        print(f"\n❌ API连接测试失败")
        print("\n可能的解决方案:")
        print("1. 检查网络连接是否正常")
        print("2. 检查是否有防火墙或代理阻止连接")
        print("3. 验证APP_ID和APP_SECRET是否正确")
        print("4. 确认飞书应用状态是否正常")
