#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书多维表格API操作脚本
用于向飞书多维表格新增记录
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
import urllib3
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class FeishuBitableAPI:
    """飞书多维表格API操作类"""
    
    def __init__(self, app_id: str, app_secret: str):
        """
        初始化飞书API客户端

        Args:
            app_id: 飞书应用ID
            app_secret: 飞书应用密钥
        """
        self.app_id = app_id
        self.app_secret = app_secret
        self.base_url = "https://open.feishu.cn/open-apis"
        self.access_token = None
        self.token_expires_at = 0

        # 创建会话对象，配置重试策略
        self.session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # 设置超时时间
        self.timeout = 30
        
    def get_tenant_access_token(self) -> str:
        """
        获取tenant_access_token
        
        Returns:
            str: access token
        """
        # 检查token是否过期
        if self.access_token and time.time() < self.token_expires_at:
            return self.access_token
            
        url = f"{self.base_url}/auth/v3/tenant_access_token/internal"
        
        payload = {
            "app_id": self.app_id,
            "app_secret": self.app_secret
        }
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        try:
            response = self.session.post(
                url,
                headers=headers,
                data=json.dumps(payload),
                timeout=self.timeout,
                verify=False  # 暂时禁用SSL验证以解决连接问题
            )
            response.raise_for_status()
            
            result = response.json()
            if result.get('code') == 0:
                self.access_token = result['tenant_access_token']
                # token有效期通常是2小时，这里设置为1.5小时后过期
                self.token_expires_at = time.time() + 5400
                print(f"成功获取access_token: {self.access_token[:20]}...")
                return self.access_token
            else:
                raise Exception(f"获取access_token失败: {result.get('msg', '未知错误')}")
                
        except requests.exceptions.RequestException as e:
            raise Exception(f"请求失败: {str(e)}")
    
    def extract_app_token_and_table_id(self, bitable_url: str) -> tuple:
        """
        从飞书多维表格URL中提取app_token和table_id
        
        Args:
            bitable_url: 飞书多维表格链接
            
        Returns:
            tuple: (app_token, table_id)
        """
        try:
            # URL格式: https://gwvwal7cgy.feishu.cn/base/TtULb7pBiaGRMgs4dfac4aLAnId
            # app_token就是base/后面的部分
            if '/base/' in bitable_url:
                app_token = bitable_url.split('/base/')[-1].split('?')[0]
                print(f"提取到app_token: {app_token}")
                
                # 对于table_id，我们需要获取第一个表格的ID
                # 这里先返回app_token，table_id需要通过API获取
                return app_token, None
            else:
                raise ValueError("无效的飞书多维表格URL格式")
                
        except Exception as e:
            raise Exception(f"解析URL失败: {str(e)}")
    
    def get_tables(self, app_token: str) -> List[Dict]:
        """
        获取多维表格中的所有数据表
        
        Args:
            app_token: 多维表格的app_token
            
        Returns:
            List[Dict]: 数据表列表
        """
        access_token = self.get_tenant_access_token()
        
        url = f"{self.base_url}/bitable/v1/apps/{app_token}/tables"
        
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        try:
            response = self.session.get(
                url,
                headers=headers,
                timeout=self.timeout,
                verify=False
            )
            response.raise_for_status()
            
            result = response.json()
            if result.get('code') == 0:
                tables = result.get('data', {}).get('items', [])
                print(f"获取到 {len(tables)} 个数据表")
                for table in tables:
                    print(f"  - 表名: {table.get('name')}, table_id: {table.get('table_id')}")
                return tables
            else:
                raise Exception(f"获取数据表失败: {result.get('msg', '未知错误')}")
                
        except requests.exceptions.RequestException as e:
            raise Exception(f"请求失败: {str(e)}")
    
    def add_record(self, app_token: str, table_id: str, fields: Dict[str, Any]) -> Dict:
        """
        新增单条记录
        
        Args:
            app_token: 多维表格的app_token
            table_id: 数据表的table_id
            fields: 字段数据，格式为 {"字段名": "字段值"}
            
        Returns:
            Dict: 新增记录的结果
        """
        access_token = self.get_tenant_access_token()
        
        url = f"{self.base_url}/bitable/v1/apps/{app_token}/tables/{table_id}/records"
        
        payload = {
            "fields": fields
        }
        
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        try:
            response = self.session.post(
                url,
                headers=headers,
                data=json.dumps(payload),
                timeout=self.timeout,
                verify=False
            )
            response.raise_for_status()
            
            result = response.json()
            if result.get('code') == 0:
                record_data = result.get('data', {}).get('record', {})
                print(f"成功新增记录，record_id: {record_data.get('record_id')}")
                return record_data
            else:
                raise Exception(f"新增记录失败: {result.get('msg', '未知错误')}")
                
        except requests.exceptions.RequestException as e:
            raise Exception(f"请求失败: {str(e)}")
    
    def add_records_batch(self, app_token: str, table_id: str, records: List[Dict[str, Any]]) -> List[Dict]:
        """
        批量新增记录
        
        Args:
            app_token: 多维表格的app_token
            table_id: 数据表的table_id
            records: 记录列表，每个记录格式为 {"fields": {"字段名": "字段值"}}
            
        Returns:
            List[Dict]: 新增记录的结果列表
        """
        access_token = self.get_tenant_access_token()
        
        url = f"{self.base_url}/bitable/v1/apps/{app_token}/tables/{table_id}/records/batch_create"
        
        payload = {
            "records": records
        }
        
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        try:
            response = self.session.post(
                url,
                headers=headers,
                data=json.dumps(payload),
                timeout=self.timeout,
                verify=False
            )
            response.raise_for_status()
            
            result = response.json()
            if result.get('code') == 0:
                records_data = result.get('data', {}).get('records', [])
                print(f"成功批量新增 {len(records_data)} 条记录")
                return records_data
            else:
                raise Exception(f"批量新增记录失败: {result.get('msg', '未知错误')}")
                
        except requests.exceptions.RequestException as e:
            raise Exception(f"请求失败: {str(e)}")


def create_sample_record() -> Dict[str, Any]:
    """
    创建示例记录数据
    
    Returns:
        Dict: 示例记录的字段数据
    """
    # 当前时间戳（毫秒）
    current_timestamp = int(datetime.now().timestamp() * 1000)
    
    fields = {
        "消息时间": current_timestamp,  # 日期格式，使用时间戳
        "发送人": "测试用户",  # 文本格式
        "消息内容": "这是一条通过API新增的测试消息",  # 文本格式
        # "消息附件": []  # 附件格式，暂时留空，需要先上传文件获取file_token
    }
    
    return fields


def main():
    """主函数"""
    # 配置信息
    APP_ID = "cli_a828491ea031d013"
    APP_SECRET = "eJsXnjJJVZh9K1XTnR7JbgxBzGbDMlRT"
    BITABLE_URL = "https://gwvwal7cgy.feishu.cn/base/TtULb7pBiaGRMgs4dfac4aLAnId"
    
    try:
        # 初始化API客户端
        api = FeishuBitableAPI(APP_ID, APP_SECRET)
        
        # 提取app_token
        app_token, _ = api.extract_app_token_and_table_id(BITABLE_URL)
        
        # 获取数据表列表
        tables = api.get_tables(app_token)
        
        if not tables:
            print("未找到任何数据表")
            return
        
        # 使用第一个表格
        table_id = tables[0]['table_id']
        table_name = tables[0]['name']
        print(f"\n使用数据表: {table_name} (ID: {table_id})")
        
        # 创建示例记录
        sample_fields = create_sample_record()
        print(f"\n准备新增记录: {sample_fields}")
        
        # 新增单条记录
        result = api.add_record(app_token, table_id, sample_fields)
        print(f"\n新增记录成功!")
        print(f"记录ID: {result.get('record_id')}")
        print(f"记录详情: {result}")
        
        # 示例：批量新增记录
        print("\n=== 批量新增示例 ===")
        batch_records = []
        for i in range(2):
            fields = {
                "消息时间": int(datetime.now().timestamp() * 1000),
                "发送人": f"批量测试用户{i+1}",
                "消息内容": f"这是第{i+1}条批量新增的测试消息"
            }
            batch_records.append({"fields": fields})
        
        batch_result = api.add_records_batch(app_token, table_id, batch_records)
        print(f"批量新增成功，共 {len(batch_result)} 条记录")
        
    except Exception as e:
        print(f"操作失败: {str(e)}")


if __name__ == "__main__":
    main()
